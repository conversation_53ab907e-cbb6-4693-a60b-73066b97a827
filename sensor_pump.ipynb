{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7d4b9aef", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import warnings\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# Machine Learning imports\n", "from sklearn.ensemble import IsolationForest\n", "from sklearn.neighbors import LocalOutlierFactor\n", "from sklearn.svm import OneClassSVM\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.cluster import DBSCAN\n", "from sklearn.decomposition import PCA\n", "\n", "# Statistical imports\n", "from scipy import stats\n", "from scipy.stats import zscore\n", "class PumpAnomalyDetector:\n", "    def __init__(self, data_path):\n", "        \"\"\"\n", "        Initialize the anomaly detector with pump sensor data\n", "        Parameters:\n", "            data_path (str): Path to CSV file with columns: _id, IP, Name, StartAdress, Count, Type, Value, DateTime\n", "        \"\"\"\n", "        self.df = pd.read_csv(data_path)\n", "        self.prepare_data()\n", "\n", "    def prepare_data(self):\n", "        \"\"\"Clean and prepare the data for analysis\"\"\"\n", "        print(\"Preparing data...\")\n", "        self.df['DateTime'] = pd.to_datetime(self.df['DateTime'])\n", "\n", "        print(\"Data quality issues found:\")\n", "        invalid_mask = (self.df['Value'] == -3276.8) | (self.df['Value'] < -1000)\n", "        invalid_count = invalid_mask.sum()\n", "        print(f\" - Invalid sensor readings (likely error codes): {invalid_count}\")\n", "\n", "        self.df['is_sensor_error'] = invalid_mask\n", "        self.df_clean = self.df[~invalid_mask].copy()\n", "        self.df_clean = self.df_clean.dropna(subset=['Value'])\n", "        self.df_clean = self.df_clean.sort_values('DateTime')\n", "        self.df_clean['hour'] = self.df_clean['DateTime'].dt.hour\n", "        self.df_clean['day_of_week'] = self.df_clean['DateTime'].dt.dayofweek\n", "\n", "        print(f\"Original data shape: {self.df.shape}\")\n", "        print(f\"Clean data shape (after removing sensor errors): {self.df_clean.shape}\")\n", "        print(f\"Date range: {self.df_clean['DateTime'].min()} to {self.df_clean['DateTime'].max()}\")\n", "        print(f\"Unique pumps (IPs): {self.df_clean['IP'].nunique()}\")\n", "        print(f\"Unique IP addresses: {sorted(self.df_clean['IP'].unique())}\")\n", "        print(f\"Unique features (Names): {self.df_clean['Name'].nunique()}\")\n", "        print(f\"Feature types: {sorted(self.df_clean['Name'].unique())}\")\n", "\n", "        print(\"\\nValue ranges by sensor type:\")\n", "        for name in sorted(self.df_clean['Name'].unique()):\n", "            sensor_data = self.df_clean[self.df_clean['Name'] == name]['Value']\n", "            print(f\" {name}: {sensor_data.min():.2f} to {sensor_data.max():.2f} (mean: {sensor_data.mean():.2f})\")\n", "\n", "        self.df = self.df_clean\n", "\n", "    def statistical_anomaly_detection(self, method='zscore', threshold=3):\n", "        \"\"\"Detect anomalies using statistical methods\"\"\"\n", "        print(f\"\\nDetecting anomalies using {method} method...\")\n", "        anomalies = []\n", "\n", "        for (ip, name), group in self.df.groupby(['IP', 'Name']):\n", "            values = group['Value'].values\n", "\n", "            if method == 'zscore':\n", "                z_scores = np.abs(zscore(values))\n", "                anomaly_mask = z_scores > threshold\n", "            elif method == 'modified_zscore':\n", "                median = np.median(values)\n", "                mad = np.median(np.abs(values - median))\n", "                modified_z_scores = 0.6745 * (values - median) / mad\n", "                anomaly_mask = np.abs(modified_z_scores) > threshold\n", "            elif method == 'iqr':\n", "                Q1 = np.percentile(values, 25)\n", "                Q3 = np.percentile(values, 75)\n", "                IQR = Q3 - Q1\n", "                lower_bound = Q1 - 1.5 * IQR\n", "                upper_bound = Q3 + 1.5 * IQR\n", "                anomaly_mask = (values < lower_bound) | (values > upper_bound)\n", "\n", "            group_copy = group.copy()\n", "            group_copy['is_anomaly_statistical'] = anomaly_mask\n", "            group_copy['anomaly_method'] = method\n", "            anomalies.append(group_copy)\n", "\n", "        self.df_with_statistical = pd.concat(anomalies, ignore_index=True)\n", "        anomaly_count = self.df_with_statistical['is_anomaly_statistical'].sum()\n", "        print(f\"Statistical anomalies detected: {anomaly_count} ({anomaly_count / len(self.df_with_statistical) * 100:.2f}%)\")\n", "        return self.df_with_statistical[self.df_with_statistical['is_anomaly_statistical']]\n", "\n", "    def ml_anomaly_detection(self, methods=['isolation_forest'], contamination=0.1):\n", "        \"\"\"Detect anomalies using machine learning methods\"\"\"\n", "        print(f\"\\nDetecting anomalies using ML methods: {methods}\")\n", "        pivot_df = self.df.pivot_table(index=['DateTime', 'IP'], columns='Name', values='Value', aggfunc='mean').reset_index()\n", "        feature_columns = [col for col in pivot_df.columns if col not in ['DateTime', 'IP']]\n", "        pivot_df[feature_columns] = pivot_df[feature_columns].fillna(pivot_df[feature_columns].mean())\n", "\n", "        scaler = StandardScaler()\n", "        X_scaled = scaler.fit_transform(pivot_df[feature_columns])\n", "        anomaly_results = pivot_df[['DateTime', 'IP']].copy()\n", "\n", "        for method in methods:\n", "            print(f\"Applying {method}...\")\n", "            if method == 'isolation_forest':\n", "                model = IsolationForest(contamination=contamination, random_state=42)\n", "                anomaly_pred = model.fit_predict(X_scaled)\n", "                anomaly_results[f'{method}_anomaly'] = (anomaly_pred == -1)\n", "            elif method == 'lof':\n", "                model = LocalOutlierFactor(contamination=contamination)\n", "                anomaly_pred = model.fit_predict(X_scaled)\n", "                anomaly_results[f'{method}_anomaly'] = (anomaly_pred == -1)\n", "            elif method == 'one_class_svm':\n", "                model = OneClassSVM(gamma='scale', nu=contamination)\n", "                anomaly_pred = model.fit_predict(X_scaled)\n", "                anomaly_results[f'{method}_anomaly'] = (anomaly_pred == -1)\n", "            elif method == 'dbscan':\n", "                model = DBSCAN(eps=0.5, min_samples=5)\n", "                cluster_pred = model.fit_predict(X_scaled)\n", "                anomaly_results[f'{method}_anomaly'] = (cluster_pred == -1)\n", "\n", "        self.ml_anomalies = anomaly_results\n", "        self.df_with_ml = self.df.merge(anomaly_results, on=['DateTime', 'IP'], how='left')\n", "\n", "        for method in methods:\n", "            anomaly_count = anomaly_results[f'{method}_anomaly'].sum()\n", "            print(f\"{method} anomalies detected: {anomaly_count} ({anomaly_count / len(anomaly_results) * 100:.2f}%)\")\n", "\n", "        return anomaly_results\n", "\n", "    def time_series_anomaly_detection(self, window_size=24):\n", "        \"\"\"Detect anomalies using time series specific methods\"\"\"\n", "        print(f\"\\nDetecting time series anomalies with window size {window_size}...\")\n", "        anomalies = []\n", "\n", "        for (ip, name), group in self.df.groupby(['IP', 'Name']):\n", "            group = group.sort_values('DateTime').copy()\n", "            group['rolling_mean'] = group['Value'].rolling(window=window_size, center=True).mean()\n", "            group['rolling_std'] = group['Value'].rolling(window=window_size, center=True).std()\n", "            group['upper_bound'] = group['rolling_mean'] + 2 * group['rolling_std']\n", "            group['lower_bound'] = group['rolling_mean'] - 2 * group['rolling_std']\n", "            group['is_anomaly_ts'] = (group['Value'] > group['upper_bound']) | (group['Value'] < group['lower_bound'])\n", "            group['value_diff'] = group['Value'].diff()\n", "            group['value_diff_zscore'] = np.abs(zscore(group['value_diff'].fillna(0)))\n", "            group['is_anomaly_rate_change'] = group['value_diff_zscore'] > 3\n", "            anomalies.append(group)\n", "\n", "        self.df_with_ts = pd.concat(anomalies, ignore_index=True)\n", "        ts_anomaly_count = self.df_with_ts['is_anomaly_ts'].sum()\n", "        rate_anomaly_count = self.df_with_ts['is_anomaly_rate_change'].sum()\n", "        print(f\"Time series anomalies detected: {ts_anomaly_count} ({ts_anomaly_count / len(self.df_with_ts) * 100:.2f}%)\")\n", "        print(f\"Rate of change anomalies detected: {rate_anomaly_count} ({rate_anomaly_count / len(self.df_with_ts) * 100:.2f}%)\")\n", "        return self.df_with_ts[self.df_with_ts['is_anomaly_ts'] | self.df_with_ts['is_anomaly_rate_change']]\n"]}, {"cell_type": "code", "execution_count": 3, "id": "df3b49d6", "metadata": {}, "outputs": [], "source": ["# Example usage and demonstration\n", "def main():\n", "    \"\"\"Main function demonstrating how to use the PumpAnomalyDetector\"\"\"\n", "    print(\"Example usage of PumpAnomalyDetector:\")\n", "\n", "    print(\"\\n1. Initialize detector:\")\n", "    print(\" detector = PumpAnomalyDetector('your_sensor_data.csv')\")\n", "\n", "    print(\"\\n2. Run statistical anomaly detection:\")\n", "    print(\" statistical_anomalies = detector.statistical_anomaly_detection(method='zscore', threshold=3)\")\n", "\n", "    print(\"\\n3. Run ML-based anomaly detection:\")\n", "    print(\" ml_anomalies = detector.ml_anomaly_detection(methods=['isolation_forest', 'lof'])\")\n", "\n", "    print(\"\\n4. Run time series anomaly detection:\")\n", "    print(\" ts_anomalies = detector.time_series_anomaly_detection(window_size=24)\")\n", "\n", "    print(\"\\n5. Visualize results:\")\n", "    print(\" detector.visualize_anomalies(pump_ip='*************', feature_name='dp temperature')\")\n", "\n", "    print(\"\\n6. Generate comprehensive report:\")\n", "    print(\" detector.generate_anomaly_report()\")"]}, {"cell_type": "code", "execution_count": null, "id": "be0098db", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bd943e5a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2a109dbf", "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "\n", "\n", "\n", "\n", "\n", "def analyze_pump_data_sample():\n", "\n", "    df = pd.read_csv('sample_pump_data.csv')\n", "    df['DateTime'] = pd.to_datetime(df['DateTime'])\n", "\n", "    print(f\"Total records: {len(df)}\")\n", "    print(f\"Unique pumps: {df['IP'].nunique()}\")\n", "    print(f\"Pump IPs: {sorted(df['IP'].unique())}\")\n", "    print(f\"Unique sensors: {df['Name'].nunique()}\")\n", "    print(f\"Sensor types: {sorted(df['Name'].unique())}\")\n", "\n", "    invalid_readings = (df['Value'] == -3276.8).sum()\n", "    print(f\"\\nData quality issues:\")\n", "    print(f\"Invalid sensor readings (-3276.8): {invalid_readings}\")\n", "\n", "    print(f\"\\nValue ranges by sensor type:\")\n", "    clean_df = df[df['Value'] != -3276.8]\n", "    for sensor in sorted(clean_df['Name'].unique()):\n", "        sensor_values = clean_df[clean_df['Name'] == sensor]['Value']\n", "        print(f\" {sensor}: {sensor_values.min():.1f} to {sensor_values.max():.1f}\")\n", "\n", "    return df\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    analyze_pump_data_sample()\n", "\n", "    print(\"\\n\" + \"=\" * 70)\n", "    print(\"USAGE INSTRUCTIONS FOR YOUR DATA:\")\n", "    print(\"=\" * 70)\n", "    print(\"\\n1. Save your CSV data with proper column names:\")\n", "    print(\" _id, IP, Name, StartAdress, Count, Type, Value, DateTime\")\n", "\n", "    print(\"\\n2. Initialize the detector:\")\n", "    print(\" detector = PumpAnomalyDetector('your_pump_data.csv')\")\n", "\n", "    print(\"\\n3. Run anomaly detection:\")\n", "    print(\" # Statistical approach (recommended first)\")\n", "    print(\" statistical_anomalies = detector.statistical_anomaly_detection(method='zscore', threshold=3)\")\n", "\n", "    print(\"\\n # Machine learning approach\")\n", "    print(\" ml_anomalies = detector.ml_anomaly_detection(methods=['isolation_forest'])\")\n", "\n", "    print(\"\\n # Time series approach\")\n", "    print(\" ts_anomalies = detector.time_series_anomaly_detection(window_size=10)\")\n", "\n", "    print(\"\\n4. Visualize specific pump and sensor:\")\n", "    print(\" detector.visualize_anomalies(pump_ip='*************', feature_name='DP Temperature')\")\n", "\n", "    print(\"\\n5. Generate comprehensive report:\")\n", "    print(\" detector.generate_anomaly_report()\")\n", "\n", "    print(\"\\nKEY OBSERVATIONS FROM YOUR DATA:\")\n", "    print(\"- Sensor error codes like -3276.8 are automatically detected and excluded\")\n", "    print(\"- Different sensors have very different value ranges (0.3 to 109°C)\")\n", "    print(\"- Temperature sensors show normal operating ranges (~50-110°C)\")\n", "    print(\"- Power consumption is quite low (0.3-2.2)\")\n", "    print(\"- Motor speeds around 100 RPM appear normal\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "d137fa9a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9d973dca", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d7737ea9", "metadata": {}, "outputs": [], "source": ["# Additional utility functions\n", "def create_synthetic_pump_data(n_samples=10000, n_pumps=4, n_features=12, anomaly_rate=0.05):\n", "    \"\"\"Create synthetic pump sensor data for testing\"\"\"\n", "    np.random.seed(42)\n", "    pump_ips = [f\"192.168.1.{100+i}\" for i in range(n_pumps)]\n", "    feature_names = [\n", "        'dp temperature', 'dp cooling', 'pressure', 'flow_rate', 'vibration', 'power_consumption',\n", "        'efficiency', 'rpm', 'temperature_inlet', 'temperature_outlet', 'noise_level', 'wear_indicator'\n", "    ][:n_features]\n", "\n", "    data = []\n", "    start_date = datetime(2024, 1, 1)\n", "\n", "    for i in range(n_samples):\n", "        timestamp = start_date + pd.Timedel<PERSON>(hours=i * 0.1)\n", "        for pump_ip in pump_ips:\n", "            for j, feature_name in enumerate(feature_names):\n", "                base_value = 50 + j * 10\n", "                seasonal_component = 10 * np.sin(2 * np.pi * i / (24 * 10))\n", "                noise = np.random.normal(0, 2)\n", "                normal_value = base_value + seasonal_component + noise\n", "\n", "                if np.random.random() < anomaly_rate:\n", "                    anomaly_multiplier = np.random.choice([0.3, 2.5])\n", "                    value = normal_value * anomaly_multiplier\n", "                else:\n", "                    value = normal_value\n", "\n", "                data.append({\n", "                    'Id': len(data) + 1,\n", "                    'IP': pump_ip,\n", "                    'Name': feature_name,\n", "                    'Start adreess': 1000 + j,\n", "                    'Value': round(value, 2),\n", "                    'Datetime': timestamp\n", "                })\n", "\n", "    df = pd.DataFrame(data)\n", "    return df"]}], "metadata": {"kernelspec": {"display_name": "sensor_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}

# sensor_anomaly_pipeline.py

"""
This script performs end-to-end sensor pump anomaly detection using:
1. Data loading and preprocessing
2. Statistical anomaly detection (Z-score, IQR, 3-sigma)
3. Machine learning anomaly detection (Isolation Forest, One-Class SVM)
4. Time series forecasting (ARIMA, Prophet)
5. MongoDB stream listener for real-time ingestion
6. FastAPI server for real-time anomaly reporting
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import zscore
from sklearn.ensemble import IsolationForest
from sklearn.svm import OneClassSVM
from statsmodels.tsa.arima.model import ARIMA
from prophet import Prophet
from pymongo import MongoClient
from fastapi import FastAPI
from pydantic import BaseModel
import uvicorn


def preprocess_data(filepath):
    df = pd.read_csv(filepath)
    """
    Comprehensive data preprocessing for sensor data
    """
    print("=== DATA PREPROCESSING ===")
    # Convert DateTime to proper datetime format
    df['DateTime'] = pd.to_datetime(df['DateTime'])

    # Create a copy for processing
    processed_df = df.copy()

    # Create unique sensor identifier combining IP and Name
    processed_df['sensor_id'] = processed_df['IP'] + '_' + processed_df['Name']

    # Sort by sensor and time
    processed_df = processed_df.sort_values(['sensor_id', 'DateTime'])

    # Initialize results dictionary
    preprocessing_results = {
        'original_shape': df.shape,
        'sensors_found': processed_df['sensor_id'].nunique(),
        'ip_addresses': processed_df['IP'].nunique(),
        'sensor_types': processed_df['Name'].nunique(),
        'time_range': (processed_df['DateTime'].min(), processed_df['DateTime'].max())
    }

    # Identify obvious error values (like -3276.8)
    error_values = [-3276.8, -32768, 32767, np.inf, -np.inf]
    error_mask = processed_df['Value'].isin(error_values)
    preprocessing_results['error_values_found'] = error_mask.sum()

    # Mark error values
    processed_df['is_error_value'] = error_mask

    # Create pivot table for time series analysis (one column per sensor)
    df_pivot = processed_df.pivot_table(
        index='DateTime',
        columns='sensor_id',
        values='Value',
        aggfunc='mean'
    ).fillna(method='ffill').fillna(method='bfill')

    # Calculate basic statistics for each sensor
    sensor_stats = {}
    for sensor in processed_df['sensor_id'].unique():
        sensor_data = processed_df[processed_df['sensor_id'] == sensor]['Value']
        clean_data = sensor_data[~sensor_data.isin(error_values)]
        if len(clean_data) > 0:
            sensor_stats[sensor] = {
                'mean': clean_data.mean(),
                'std': clean_data.std(),
                'min': clean_data.min(),
                'max': clean_data.max(),
                'error_rate': (len(sensor_data) - len(clean_data)) / len(sensor_data) * 100
            }
    # self.sensor_stats = sensor_stats
    preprocessing_results['sensor_statistics'] = sensor_stats
    print(f"Original data shape: {preprocessing_results['original_shape']}")
    print(f"Number of unique sensors: {preprocessing_results['sensors_found']}")
    print(f"Number of IP addresses: {preprocessing_results['ip_addresses']}")
    print(f"Time range: {preprocessing_results['time_range'][0]} to {preprocessing_results['time_range'][1]}")
    print(f"Error values found: {preprocessing_results['error_values_found']}")

    return df_pivot, preprocessing_results, processed_df, 
        
# ------------------------- 1. LOAD & PREPROCESS -------------------------
def load_and_preprocess(filepath):
    df = pd.read_csv(filepath)
    df['DateTime'] = pd.to_datetime(df['DateTime'], errors='coerce')
    df = df.dropna(subset=['DateTime', 'Value'])
    df = df[df['Value'] > -1000]  # remove invalid values
    df_pivot = df.pivot_table(index='DateTime', columns='Name', values='Value', aggfunc='mean').sort_index()
    df_pivot = df_pivot.ffill().bfill()  # fill missing data
    return df_pivot

# ------------------- 2. STATISTICAL ANOMALY DETECTION ------------------
def statistical_anomaly_detection(df_pivot):
    # z_scores = df_pivot.apply(zscore)
    z_scores = df_pivot.select_dtypes(include='number').apply(zscore)

    z_anomalies = (np.abs(z_scores) > 3)

    Q1 = df_pivot.quantile(0.25)
    Q3 = df_pivot.quantile(0.75)
    IQR = Q3 - Q1
    iqr_anomalies = (df_pivot < (Q1 - 1.5 * IQR)) | (df_pivot > (Q3 + 1.5 * IQR))

    mean_vals = df_pivot.mean()
    std_vals = df_pivot.std()
    sigma_anomalies = (df_pivot < (mean_vals - 3 * std_vals)) | (df_pivot > (mean_vals + 3 * std_vals))

    summary = pd.DataFrame({
        "Z-Score": z_anomalies.sum(),
        "IQR": iqr_anomalies.sum(),
        "3-Sigma": sigma_anomalies.sum()
    })
    return summary, z_anomalies, iqr_anomalies, sigma_anomalies

# ------------------ 3. MACHINE LEARNING ANOMALY DETECTION --------------
def ml_anomaly_detection(df_pivot):
    df_result = df_pivot.copy()
    model_if = IsolationForest(contamination=0.01)
    df_result["IF_Anomaly"] = model_if.fit_predict(df_result)
    df_result["IF_Flag"] = df_result["IF_Anomaly"] == -1

    model_svm = OneClassSVM(nu=0.01, kernel="rbf", gamma="auto")
    df_result["SVM_Anomaly"] = model_svm.fit_predict(df_result.iloc[:, :-2])
    df_result["SVM_Flag"] = df_result["SVM_Anomaly"] == -1
    return df_result

# ---------------------- 4. TIME SERIES FORECASTING ---------------------
def arima_forecast(series, steps=10):
    model = ARIMA(series, order=(5,1,0))
    model_fit = model.fit()
    forecast = model_fit.forecast(steps=steps)
    return forecast

def prophet_forecast(series, steps=10):
    df_prophet = series.reset_index()
    df_prophet.columns = ['ds', 'y']
    m = Prophet()
    m.fit(df_prophet)
    future = m.make_future_dataframe(periods=steps)
    forecast = m.predict(future)
    return forecast[['ds', 'yhat', 'yhat_lower', 'yhat_upper']]

# -------------------------- 5. VISUALIZATION ----------------------------
def visualize_anomalies(df_pivot, z_anomalies, iqr_anomalies, sigma_anomalies):
    for col in df_pivot.columns:
        plt.figure(figsize=(14, 5))
        plt.plot(df_pivot.index, df_pivot[col], label='Value', color='blue')
        if col in z_anomalies:
            plt.scatter(df_pivot.index[z_anomalies[col]], df_pivot[col][z_anomalies[col]], color='red', label='Z-Score')
        if col in iqr_anomalies:
            plt.scatter(df_pivot.index[iqr_anomalies[col]], df_pivot[col][iqr_anomalies[col]], color='orange', label='IQR')
        if col in sigma_anomalies:
            plt.scatter(df_pivot.index[sigma_anomalies[col]], df_pivot[col][sigma_anomalies[col]], color='green', label='3-Sigma')
        plt.title(f"Sensor Anomalies: {col}")
        plt.legend()
        plt.tight_layout()
        plt.savefig(f"anomaly_{col}.png")
        plt.close()

def visualize_forecast(series, forecast):
    plt.figure(figsize=(14, 5))
    plt.plot(series.index, series.values, label='Original')
    plt.plot(forecast['ds'], forecast['yhat'], label='Forecast', color='magenta')
    plt.fill_between(forecast['ds'], forecast['yhat_lower'], forecast['yhat_upper'], alpha=0.3)
    plt.title('Time Series Forecast')
    plt.legend()
    plt.tight_layout()
    plt.savefig("time_series_forecast.png")
    plt.close()

# ------------------------ 6. MONGODB STREAMING --------------------------
def listen_to_mongodb():
    client = MongoClient("mongodb://localhost:27017/")
    db = client['pump_db']
    collection = db['sensor_data']
    with collection.watch() as stream:
        for change in stream:
            print("Change detected:", change)

# ------------------------- 7. FASTAPI ENDPOINT --------------------------
app = FastAPI()

class SensorInput(BaseModel):
    DateTime: str
    sensor_values: dict

@app.post("/detect")
def detect_anomaly(input: SensorInput):
    df_input = pd.DataFrame([input.sensor_values])
    model = IsolationForest(contamination=0.01)
    result = model.fit_predict(df_input)
    return {"anomaly": result[0] == -1}

# ----------------------------- MAIN SCRIPT ------------------------------
if __name__ == "__main__":
    file_path = r"C:\Python_Projects\sensor_pump\sensor_data\Pump *************.csv"
    df_clean, _, _ = preprocess_data(file_path)
    # df_clean = load_and_preprocess(file_path)
    

    summary, z_anomalies, iqr_anomalies, sigma_anomalies = statistical_anomaly_detection(df_clean)
    print("\nStatistical Anomaly Summary:\n", summary)
    visualize_anomalies(df_clean, z_anomalies, iqr_anomalies, sigma_anomalies)

    df_with_ml = ml_anomaly_detection(df_clean)
    df_with_ml.to_csv("ml_anomaly_result.csv")
    print("\nSaved ML-based anomaly detection results to ml_anomaly_result.csv")

    sensor = df_clean.columns[0]
    forecast = prophet_forecast(df_clean[sensor])
    visualize_forecast(df_clean[sensor], forecast)
    print("\nForecast plotted for sensor:", sensor)

    # Optional: To run API server
    # uvicorn.run(app, host="0.0.0.0", port=8000)

    # Optional: MongoDB listener
    # listen_to_mongodb()

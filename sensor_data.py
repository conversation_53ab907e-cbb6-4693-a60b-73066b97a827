import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import IsolationForest
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.svm import OneClassSVM
from sklearn.model_selection import train_test_split
import joblib
import os
import warnings

warnings.filterwarnings('ignore')

class SensorAnomalyDetector:
    def __init__(self):
        self.scalers = {}
        self.models = {}
        self.thresholds = {}
        self.sensor_stats = {}
        self.anomaly_history = []

    def preprocess_data(self, df):
        """
        Comprehensive data preprocessing for sensor data
        """
        print("=== DATA PREPROCESSING ===")
        # Convert DateTime to proper datetime format
        df['DateTime'] = pd.to_datetime(df['DateTime'])

        # Create a copy for processing
        processed_df = df.copy()

        # Create unique sensor identifier combining IP and Name
        processed_df['sensor_id'] = processed_df['IP'] + '_' + processed_df['Name']

        # Sort by sensor and time
        processed_df = processed_df.sort_values(['sensor_id', 'DateTime'])

        # Initialize results dictionary
        preprocessing_results = {
            'original_shape': df.shape,
            'sensors_found': processed_df['sensor_id'].nunique(),
            'ip_addresses': processed_df['IP'].nunique(),
            'sensor_types': processed_df['Name'].nunique(),
            'time_range': (processed_df['DateTime'].min(), processed_df['DateTime'].max())
        }

        # Identify obvious error values (like -3276.8)
        error_values = [-3276.8, -32768, 32767, np.inf, -np.inf]
        error_mask = processed_df['Value'].isin(error_values)
        preprocessing_results['error_values_found'] = error_mask.sum()

        # Mark error values
        processed_df['is_error_value'] = error_mask

        # Create pivot table for time series analysis (one column per sensor)
        pivot_df = processed_df.pivot_table(
            index='DateTime',
            columns='sensor_id',
            values='Value',
            aggfunc='mean'
        ).fillna(method='ffill').fillna(method='bfill')

        # Calculate basic statistics for each sensor
        sensor_stats = {}
        for sensor in processed_df['sensor_id'].unique():
            sensor_data = processed_df[processed_df['sensor_id'] == sensor]['Value']
            clean_data = sensor_data[~sensor_data.isin(error_values)]
            if len(clean_data) > 0:
                sensor_stats[sensor] = {
                    'mean': clean_data.mean(),
                    'std': clean_data.std(),
                    'min': clean_data.min(),
                    'max': clean_data.max(),
                    'error_rate': (len(sensor_data) - len(clean_data)) / len(sensor_data) * 100
                }
        self.sensor_stats = sensor_stats
        preprocessing_results['sensor_statistics'] = sensor_stats

        print(f"Original data shape: {preprocessing_results['original_shape']}")
        print(f"Number of unique sensors: {preprocessing_results['sensors_found']}")
        print(f"Number of IP addresses: {preprocessing_results['ip_addresses']}")
        print(f"Time range: {preprocessing_results['time_range'][0]} to {preprocessing_results['time_range'][1]}")
        print(f"Error values found: {preprocessing_results['error_values_found']}")

        return processed_df, pivot_df, preprocessing_results

    def statistical_anomaly_detection(self, df):
        """
        Statistical methods: Z-score, IQR, and 3-sigma rule
        """
        print("\n=== STATISTICAL ANOMALY DETECTION ===")
        anomalies = []
        results = {'z_score': {}, 'iqr': {}, 'three_sigma': {}}

        for sensor in df['sensor_id'].unique():
            sensor_data = df[df['sensor_id'] == sensor].copy()
            clean_data = sensor_data[~sensor_data['is_error_value']]['Value']

            if len(clean_data) < 10:  # Skip if insufficient data
                continue

            mean_val = clean_data.mean()
            std_val = clean_data.std()
            q1 = clean_data.quantile(0.25)
            q3 = clean_data.quantile(0.75)
            iqr = q3 - q1

            # Z-score method (threshold = 3)
            z_scores = np.abs((sensor_data['Value'] - mean_val) / std_val)
            z_anomalies = sensor_data[z_scores > 3]
            results['z_score'][sensor] = len(z_anomalies)

            # IQR method
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            iqr_anomalies = sensor_data[
                (sensor_data['Value'] < lower_bound) | (sensor_data['Value'] > upper_bound)
            ]
            results['iqr'][sensor] = len(iqr_anomalies)

            # 3-sigma rule
            sigma_anomalies = sensor_data[
                (sensor_data['Value'] < mean_val - 3 * std_val) |
                (sensor_data['Value'] > mean_val + 3 * std_val)
            ]
            results['three_sigma'][sensor] = len(sigma_anomalies)

            # Collect all anomalies (from z_score method as example)
            for _, row in z_anomalies.iterrows():
                anomalies.append({
                    'method': 'z_score',
                    'sensor_id': sensor,
                    'timestamp': row['DateTime'],
                    'value': row['Value'],
                    'score': z_scores.loc[row.name]
                })
            
                        # Collect all anomalies (from z_score method as example)
            for _, row in iqr_anomalies.iterrows():
                anomalies.append({
                    'method': 'iqr',
                    'sensor_id': sensor,
                    'timestamp': row['DateTime'],
                    'value': row['Value'],
                    'lower_bound': lower_bound,
                    'upper_bound': upper_bound
                })
                
                        # Collect all anomalies (from z_score method as example)
            for _, row in sigma_anomalies.iterrows():
                anomalies.append({
                    'method': '3-sigma',
                    'sensor_id': sensor,
                    'timestamp': row['DateTime'],
                    'value': row['Value'],
                    'neg_3-sigma': mean_val - 3 * std_val,
                    'pos_3-sigma': mean_val + 3 * std_val
                })

        print(f"Statistical anomalies detected across all sensors:")
        for method, method_results in results.items():
            total = sum(method_results.values())
            print(f"  {method}: {total} anomalies")
            
        statistical_anomalies_df = pd.DataFrame(anomalies)
        statistical_anomalies_df.to_csv("153_statistical_anomalies_df.csv", index=False)

        return anomalies, results

    def machine_learning_anomaly_detection(self, pivot_df):
        """
        ML-based anomaly detection using multiple algorithms
        """
        print("\n=== MACHINE LEARNING ANOMALY DETECTION ===")
        # Prepare data for ML (remove NaN values)
        ml_data = pivot_df.fillna(pivot_df.mean()).values

        # Standardize the data
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(ml_data)

        ml_results = {}

        # 1. Isolation Forest
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        iso_anomalies = iso_forest.fit_predict(scaled_data)
        ml_results['isolation_forest'] = {
            'anomalies': np.sum(iso_anomalies == -1),
            'normal': np.sum(iso_anomalies == 1),
            'anomaly_indices': np.where(iso_anomalies == -1)[0]
        }
        df_result = pivot_df.copy()
        df_result["IF_Anomaly"] = iso_anomalies
        df_result["IF_Flag"] = df_result["IF_Anomaly"] == -1
        
        
        # 2. One-Class SVM
        svm_model = OneClassSVM(nu=0.1, kernel='rbf', gamma='scale')
        svm_anomalies = svm_model.fit_predict(scaled_data)
        ml_results['one_class_svm'] = {
            'anomalies': np.sum(svm_anomalies == -1),
            'normal': np.sum(svm_anomalies == 1),
            'anomaly_indices': np.where(svm_anomalies == -1)[0]
        }
        
        df_result["SVM_Anomaly"] = iso_anomalies
        df_result["SVM_Flag"] = df_result["IF_Anomaly"] == -1
        
        # 3. DBSCAN Clustering
        dbscan = DBSCAN(eps=0.5, min_samples=5)
        clusters = dbscan.fit_predict(scaled_data)
        dbscan_anomalies = np.sum(clusters == -1)
        ml_results['dbscan'] = {
            'anomalies': dbscan_anomalies,
            'clusters': len(set(clusters)) - (1 if -1 in clusters else 0),
            'anomaly_indices': np.where(clusters == -1)[0]
        }
        
        df_result["DBSCAN_Anomaly"] = iso_anomalies
        df_result["DBSCAN_Flag"] = df_result["IF_Anomaly"] == -1
        
        # Store models for future predictions
        self.models = {
            'isolation_forest': iso_forest,
            'one_class_svm': svm_model,
            'scaler': scaler
        }
        
        df_result.to_csv("153_all_ml_anomaly_result.csv")
        
        print("ML Anomaly Detection Results:")
        for method, result in ml_results.items():
            print(f"  {method}: {result['anomalies']} anomalies detected")
        
        
       
        # Define model directory relative to script
        model_dir = os.path.join(os.getcwd(), 'models')
        os.makedirs(model_dir, exist_ok=True)

        # Save models
        joblib.dump(iso_forest, os.path.join(model_dir, 'isolation_forest_model.pkl'))
        joblib.dump(svm_model, os.path.join(model_dir, 'one_class_svm_model.pkl'))
        joblib.dump(scaler, os.path.join(model_dir, 'scaler.pkl'))

        return ml_results

    def time_series_anomaly_detection(self, df):
        """
        Time series based anomaly detection
        """
        print("\n=== TIME SERIES ANOMALY DETECTION ===")
        ts_results = {}

        for sensor in df['sensor_id'].unique():
            sensor_data = df[df['sensor_id'] == sensor].copy()
            clean_data = sensor_data[~sensor_data['is_error_value']].copy()

            if len(clean_data) < 20:  # Need enough data for time series analysis
                continue

            clean_data = clean_data.sort_values('DateTime')

            # Calculate rolling statistics
            window_size = min(10, len(clean_data) // 4)
            clean_data['rolling_mean'] = clean_data['Value'].rolling(window=window_size).mean()
            clean_data['rolling_std'] = clean_data['Value'].rolling(window=window_size).std()

            # Detect anomalies based on deviation from rolling mean
            clean_data['deviation'] = np.abs(clean_data['Value'] - clean_data['rolling_mean'])
            threshold = clean_data['rolling_std'].mean() * 2
            ts_anomalies = clean_data[clean_data['deviation'] > threshold]

            # Rate of change anomalies
            clean_data['rate_of_change'] = clean_data['Value'].diff().abs()
            roc_threshold = clean_data['rate_of_change'].quantile(0.95)
            roc_anomalies = clean_data[clean_data['rate_of_change'] > roc_threshold]

            ts_results[sensor] = {
                'rolling_anomalies': len(ts_anomalies),
                'rate_change_anomalies': len(roc_anomalies)
            }

        total_rolling = sum(result['rolling_anomalies'] for result in ts_results.values())
        total_roc = sum(result['rate_change_anomalies'] for result in ts_results.values())

        print(f"Time Series Anomalies:")
        print(f"  Rolling window anomalies: {total_rolling}")
        print(f"  Rate of change anomalies: {total_roc}")
        return ts_results

    def predict_future_anomalies(self, new_data):
        """
        Predict anomalies in new incoming data
        """
        print("\n=== FUTURE ANOMALY PREDICTION ===")
        
        # Load models if not already loaded
        if not hasattr(self, 'models') or not self.models:
            model_dir = os.path.join(os.getcwd(), 'models')  # Directory where models are stored
            model_files = {
                'isolation_forest': os.path.join(model_dir, 'isolation_forest_model.pkl'),
                'one_class_svm': os.path.join(model_dir, 'one_class_svm_model.pkl'),
                'scaler': os.path.join(model_dir, 'scaler.pkl')
            }
            
            # Check if all model files exist
            if all(os.path.exists(path) for path in model_files.values()):
                self.models = {
                    key: joblib.load(path) for key, path in model_files.items()
                }
            else:
                print("Error: Model files not found in 'models/' directory. Run training first.")
                return None

        # Preprocess new data similarly
        new_data['sensor_id'] = new_data['IP'] + '_' + new_data['Name']

        # Create pivot table for new data
        new_pivot = new_data.pivot_table(
            index='DateTime',
            columns='sensor_id',
            values='Value',
            aggfunc='mean'
        ).fillna(method='ffill').fillna(method='bfill')

        # Scale new data using existing scaler
        scaled_new_data = self.models['scaler'].transform(new_pivot.fillna(0).values)

        # Predict using trained models
        iso_predictions = self.models['isolation_forest'].predict(scaled_new_data)
        svm_predictions = self.models['one_class_svm'].predict(scaled_new_data)
        

        # Combine predictions
        predictions = {
            'isolation_forest': iso_predictions,
            'one_class_svm': svm_predictions,
            'combined_anomaly_score': (iso_predictions + svm_predictions) / 2
        }

        anomaly_count = np.sum(predictions['combined_anomaly_score'] < 0)
        print(f"Future anomalies predicted: {anomaly_count} out of {len(scaled_new_data)} data points")
        
        df_result = new_pivot.copy()
        df_result["IF_Anomaly"] = iso_predictions
        df_result["IF_Flag"] = df_result["IF_Anomaly"] == -1
        df_result["SVM_Anomaly"] = iso_predictions
        df_result["SVM_Flag"] = df_result["IF_Anomaly"] == -1
        df_result.to_csv("153_all_pred_ml_anomaly_result.csv")
        return predictions

    def generate_comprehensive_report(self, df, statistical_results, ml_results, ts_results):
        """
        Generate a comprehensive anomaly detection report
        """
        print("\n" + "="*50)
        print("COMPREHENSIVE ANOMALY DETECTION REPORT")
        print("="*50)

        # Summary statistics
        total_records = len(df)
        total_sensors = df['sensor_id'].nunique()
        total_ips = df['IP'].nunique()

        print(f"\nDATA SUMMARY:")
        print(f"Total records: {total_records:,}")
        print(f"Total sensors: {total_sensors}")
        print(f"Total IP addresses: {total_ips}")
        print(f"Time span: {df['DateTime'].min()} to {df['DateTime'].max()}")

        # Error analysis
        error_count = df['is_error_value'].sum()
        print(f"\nERROR ANALYSIS:")
        print(f"Error values detected: {error_count:,} ({error_count/total_records*100:.2f}%)")

        # Sensors with high error rates
        high_error_sensors = []
        for sensor, stats in self.sensor_stats.items():
            if stats.get('error_rate', 0) > 10:  # More than 10% error rate
                high_error_sensors.append((sensor, stats['error_rate']))
        if high_error_sensors:
            print(f"Sensors with high error rates (>10%):")
            for sensor, rate in sorted(high_error_sensors, key=lambda x: x[1], reverse=True)[:5]:
                print(f"  {sensor}: {rate:.1f}%")

        # Statistical anomalies summary
        print(f"\nSTATISTICAL ANOMALIES:")
        for method, results in statistical_results.items():
            total = sum(results.values())
            print(f"  {method}: {total:,} anomalies")

        # ML anomalies summary
        print(f"\nMACHINE LEARNING ANOMALIES:")
        for method, results in ml_results.items():
            print(f"  {method}: {results['anomalies']:,} anomalies")

        # Time series anomalies summary
        print(f"\nTIME SERIES ANOMALIES:")
        total_rolling = sum(result['rolling_anomalies'] for result in ts_results.values())
        total_roc = sum(result['rate_change_anomalies'] for result in ts_results.values())
        print(f"  Rolling window: {total_rolling:,} anomalies")
        print(f"  Rate of change: {total_roc:,} anomalies")

        # Recommendations
        print(f"\nRECOMMENDATIONS:")
        if error_count > 0:
            print("• Investigate sensors with consistent -3276.8 values (likely sensor faults)")
        if high_error_sensors:
            print("• Priority maintenance needed for sensors with >10% error rates")
        print("• Set up real-time monitoring using the trained ML models")
        print("• Implement automated alerts for anomaly scores above threshold")

        return {
            'total_records': total_records,
            'error_rate': error_count/total_records*100,
            'high_error_sensors': high_error_sensors,
            'statistical_anomalies': statistical_results,
            'ml_anomalies': ml_results,
            'ts_anomalies': ts_results
        }
        
# Example usage function
def analyze_sensor_data(df):
    """
    Main function to run complete anomaly detection analysis
    """
    # Initialize detector
    detector = SensorAnomalyDetector()

    # Step 1: Preprocess data
    processed_df, pivot_df, preprocessing_results = detector.preprocess_data(df)

    # Step 2: Statistical anomaly detection
    statistical_anomalies, statistical_results = detector.statistical_anomaly_detection(processed_df)

    # Step 3: ML-based anomaly detection
    # ml_results = detector.machine_learning_anomaly_detection(pivot_df)

    # Step 4: Time series anomaly detection
    # ts_results = detector.time_series_anomaly_detection(processed_df)

    # Step 5: Generate comprehensive report
    # report = detector.generate_comprehensive_report(
    #     processed_df, statistical_results, ml_results, ts_results
    # )

    return detector
    # return detector, report

# Load your data
df = pd.read_csv(r'C:\Python_Projects\sensor_pump\sensor_data\Pump *************.csv')


# Split the DataFrame into training and testing sets using an 80:20 ratio
train_df, test_df = train_test_split(df, test_size=0.2, random_state=42)

# Save the training and testing sets to new CSV files
train_df.to_csv('train_data_153.csv', index=False)
test_df.to_csv('test_data_153.csv', index=False)


# Run complete analysis
detector = analyze_sensor_data(train_df)
# detector, report = analyze_sensor_data(train_df)

# For new data prediction:
# new_data = pd.read_csv(r'C:\Python_Projects\sensor_pump\sensor_data\Pump *************.csv')
predictions = detector.predict_future_anomalies(test_df)

# # Save the trained detector object for future use
# import pickle
# with open('anomaly_detector.pkl', 'wb') as f:
#     pickle.dump(detector, f)












# Filter data for plotting
plot_data = {}
for ip in ip_addresses:
    ip_data = combined_df[combined_df['IP'] == ip]
    if ip.endswith('153'):
        plot_data[ip] = {
            '10_days': ip_data[(ip_data['DateTime'] >= start_date_10_days) & (ip_data['DateTime'] <= end_date)],
            '1_month': ip_data[(ip_data['DateTime'] >= start_date_1_month) & (ip_data['DateTime'] <= end_date_1_month)]
        }
    else:
        plot_data[ip] = {
            '10_days': ip_data[(ip_data['DateTime'] >= start_date_10_days) & (ip_data['DateTime'] <= end_date)]
        }

# Plotting
for feature in features:
    plt.figure(figsize=(12, 6))
    for ip in ip_addresses:
        if ip.endswith('153'):
            plt.plot(plot_data[ip]['10_days']['DateTime'], plot_data[ip]['10_days'][feature], label=f'{ip} (10 days)')
            plt.plot(plot_data[ip]['1_month']['DateTime'], plot_data[ip]['1_month'][feature], label=f'{ip} (1 month)')
        else:
            plt.plot(plot_data[ip]['10_days']['DateTime'], plot_data[ip]['10_days'][feature], label=f'{ip} (10 days)')
    plt.title(f'{feature} over Time')
    plt.xlabel('DateTime')
    plt.ylabel(feature)
    plt.legend()
    plt.grid(True)

    # Ensure full x-axis range is shown
    plt.xlim(min(start_date_1_month, start_date_10_days), end_date)
    plt.tight_layout()
    plt.show()

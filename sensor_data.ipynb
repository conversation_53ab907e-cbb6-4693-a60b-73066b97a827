{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b2350b30", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.ensemble import IsolationForest\n", "from sklearn.cluster import DBSCAN\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.decomposition import PCA\n", "from sklearn.svm import OneClassSVM\n", "from sklearn.model_selection import train_test_split\n", "import warnings\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "class SensorAnomalyDetector:\n", "    def __init__(self):\n", "        self.scalers = {}\n", "        self.models = {}\n", "        self.thresholds = {}\n", "        self.sensor_stats = {}\n", "        self.anomaly_history = []\n", "\n", "    def preprocess_data(self, df):\n", "        \"\"\"\n", "        Comprehensive data preprocessing for sensor data\n", "        \"\"\"\n", "        print(\"=== DATA PREPROCESSING ===\")\n", "        # Convert DateTime to proper datetime format\n", "        df['DateTime'] = pd.to_datetime(df['DateTime'])\n", "\n", "        # Create a copy for processing\n", "        processed_df = df.copy()\n", "\n", "        # Create unique sensor identifier combining IP and Name\n", "        processed_df['sensor_id'] = processed_df['IP'] + '_' + processed_df['Name']\n", "\n", "        # Sort by sensor and time\n", "        processed_df = processed_df.sort_values(['sensor_id', 'DateTime'])\n", "\n", "        # Initialize results dictionary\n", "        preprocessing_results = {\n", "            'original_shape': df.shape,\n", "            'sensors_found': processed_df['sensor_id'].nunique(),\n", "            'ip_addresses': processed_df['IP'].nunique(),\n", "            'sensor_types': processed_df['Name'].nunique(),\n", "            'time_range': (processed_df['DateTime'].min(), processed_df['DateTime'].max())\n", "        }\n", "\n", "        # Identify obvious error values (like -3276.8)\n", "        error_values = [-3276.8, -32768, 32767, np.inf, -np.inf]\n", "        error_mask = processed_df['Value'].isin(error_values)\n", "        preprocessing_results['error_values_found'] = error_mask.sum()\n", "\n", "        # Mark error values\n", "        processed_df['is_error_value'] = error_mask\n", "\n", "        # Create pivot table for time series analysis (one column per sensor)\n", "        pivot_df = processed_df.pivot_table(\n", "            index='DateTime',\n", "            columns='sensor_id',\n", "            values='Value',\n", "            aggfunc='mean'\n", "        ).fillna(method='ffill').fillna(method='bfill')\n", "\n", "        # Calculate basic statistics for each sensor\n", "        sensor_stats = {}\n", "        for sensor in processed_df['sensor_id'].unique():\n", "            sensor_data = processed_df[processed_df['sensor_id'] == sensor]['Value']\n", "            clean_data = sensor_data[~sensor_data.isin(error_values)]\n", "            if len(clean_data) > 0:\n", "                sensor_stats[sensor] = {\n", "                    'mean': clean_data.mean(),\n", "                    'std': clean_data.std(),\n", "                    'min': clean_data.min(),\n", "                    'max': clean_data.max(),\n", "                    'error_rate': (len(sensor_data) - len(clean_data)) / len(sensor_data) * 100\n", "                }\n", "        self.sensor_stats = sensor_stats\n", "        preprocessing_results['sensor_statistics'] = sensor_stats\n", "\n", "        print(f\"Original data shape: {preprocessing_results['original_shape']}\")\n", "        print(f\"Number of unique sensors: {preprocessing_results['sensors_found']}\")\n", "        print(f\"Number of IP addresses: {preprocessing_results['ip_addresses']}\")\n", "        print(f\"Time range: {preprocessing_results['time_range'][0]} to {preprocessing_results['time_range'][1]}\")\n", "        print(f\"Error values found: {preprocessing_results['error_values_found']}\")\n", "\n", "        return processed_df, pivot_df, preprocessing_results\n", "\n", "    def statistical_anomaly_detection(self, df):\n", "        \"\"\"\n", "        Statistical methods: Z-score, IQR, and 3-sigma rule\n", "        \"\"\"\n", "        print(\"\\n=== STATISTICAL ANOMALY DETECTION ===\")\n", "        anomalies = []\n", "        results = {'z_score': {}, 'iqr': {}, 'three_sigma': {}}\n", "\n", "        for sensor in df['sensor_id'].unique():\n", "            sensor_data = df[df['sensor_id'] == sensor].copy()\n", "            clean_data = sensor_data[~sensor_data['is_error_value']]['Value']\n", "\n", "            if len(clean_data) < 10:  # Skip if insufficient data\n", "                continue\n", "\n", "            mean_val = clean_data.mean()\n", "            std_val = clean_data.std()\n", "            q1 = clean_data.quantile(0.25)\n", "            q3 = clean_data.quantile(0.75)\n", "            iqr = q3 - q1\n", "\n", "            # Z-score method (threshold = 3)\n", "            z_scores = np.abs((sensor_data['Value'] - mean_val) / std_val)\n", "            z_anomalies = sensor_data[z_scores > 3]\n", "            results['z_score'][sensor] = len(z_anomalies)\n", "\n", "            # IQR method\n", "            lower_bound = q1 - 1.5 * iqr\n", "            upper_bound = q3 + 1.5 * iqr\n", "            iqr_anomalies = sensor_data[\n", "                (sensor_data['Value'] < lower_bound) | (sensor_data['Value'] > upper_bound)\n", "            ]\n", "            results['iqr'][sensor] = len(iqr_anomalies)\n", "\n", "            # 3-sigma rule\n", "            sigma_anomalies = sensor_data[\n", "                (sensor_data['Value'] < mean_val - 3 * std_val) |\n", "                (sensor_data['Value'] > mean_val + 3 * std_val)\n", "            ]\n", "            results['three_sigma'][sensor] = len(sigma_anomalies)\n", "\n", "            # Collect all anomalies (from z_score method as example)\n", "            for _, row in z_anomalies.iterrows():\n", "                anomalies.append({\n", "                    'method': 'z_score',\n", "                    'sensor_id': sensor,\n", "                    'timestamp': row['DateTime'],\n", "                    'value': row['Value'],\n", "                    'score': z_scores.loc[row.name]\n", "                })\n", "\n", "        print(f\"Statistical anomalies detected across all sensors:\")\n", "        for method, method_results in results.items():\n", "            total = sum(method_results.values())\n", "            print(f\"  {method}: {total} anomalies\")\n", "        return anomalies, results\n", "\n", "    def machine_learning_anomaly_detection(self, pivot_df):\n", "        \"\"\"\n", "        ML-based anomaly detection using multiple algorithms\n", "        \"\"\"\n", "        print(\"\\n=== MACHINE LEARNING ANOMALY DETECTION ===\")\n", "        # Prepare data for ML (remove NaN values)\n", "        ml_data = pivot_df.fillna(pivot_df.mean()).values\n", "\n", "        # Standardize the data\n", "        scaler = StandardScaler()\n", "        scaled_data = scaler.fit_transform(ml_data)\n", "\n", "        ml_results = {}\n", "\n", "        # 1. Isolation Forest\n", "        iso_forest = IsolationForest(contamination=0.1, random_state=42)\n", "        iso_anomalies = iso_forest.fit_predict(scaled_data)\n", "        ml_results['isolation_forest'] = {\n", "            'anomalies': np.sum(iso_anomalies == -1),\n", "            'normal': np.sum(iso_anomalies == 1),\n", "            'anomaly_indices': np.where(iso_anomalies == -1)[0]\n", "        }\n", "\n", "        # 2. One-Class SVM\n", "        svm_model = OneClassSVM(nu=0.1, kernel='rbf', gamma='scale')\n", "        svm_anomalies = svm_model.fit_predict(scaled_data)\n", "        ml_results['one_class_svm'] = {\n", "            'anomalies': np.sum(svm_anomalies == -1),\n", "            'normal': np.sum(svm_anomalies == 1),\n", "            'anomaly_indices': np.where(svm_anomalies == -1)[0]\n", "        }\n", "\n", "        # 3. DBSCAN Clustering\n", "        dbscan = DBSCAN(eps=0.5, min_samples=5)\n", "        clusters = dbscan.fit_predict(scaled_data)\n", "        dbscan_anomalies = np.sum(clusters == -1)\n", "        ml_results['dbscan'] = {\n", "            'anomalies': dbscan_anomalies,\n", "            'clusters': len(set(clusters)) - (1 if -1 in clusters else 0),\n", "            'anomaly_indices': np.where(clusters == -1)[0]\n", "        }\n", "\n", "        # Store models for future predictions\n", "        self.models = {\n", "            'isolation_forest': iso_forest,\n", "            'one_class_svm': svm_model,\n", "            'scaler': scaler\n", "        }\n", "\n", "        print(\"ML Anomaly Detection Results:\")\n", "        for method, result in ml_results.items():\n", "            print(f\"  {method}: {result['anomalies']} anomalies detected\")\n", "        return ml_results\n", "\n", "    def time_series_anomaly_detection(self, df):\n", "        \"\"\"\n", "        Time series based anomaly detection\n", "        \"\"\"\n", "        print(\"\\n=== TIME SERIES ANOMALY DETECTION ===\")\n", "        ts_results = {}\n", "\n", "        for sensor in df['sensor_id'].unique():\n", "            sensor_data = df[df['sensor_id'] == sensor].copy()\n", "            clean_data = sensor_data[~sensor_data['is_error_value']].copy()\n", "\n", "            if len(clean_data) < 20:  # Need enough data for time series analysis\n", "                continue\n", "\n", "            clean_data = clean_data.sort_values('DateTime')\n", "\n", "            # Calculate rolling statistics\n", "            window_size = min(10, len(clean_data) // 4)\n", "            clean_data['rolling_mean'] = clean_data['Value'].rolling(window=window_size).mean()\n", "            clean_data['rolling_std'] = clean_data['Value'].rolling(window=window_size).std()\n", "\n", "            # Detect anomalies based on deviation from rolling mean\n", "            clean_data['deviation'] = np.abs(clean_data['Value'] - clean_data['rolling_mean'])\n", "            threshold = clean_data['rolling_std'].mean() * 2\n", "            ts_anomalies = clean_data[clean_data['deviation'] > threshold]\n", "\n", "            # Rate of change anomalies\n", "            clean_data['rate_of_change'] = clean_data['Value'].diff().abs()\n", "            roc_threshold = clean_data['rate_of_change'].quantile(0.95)\n", "            roc_anomalies = clean_data[clean_data['rate_of_change'] > roc_threshold]\n", "\n", "            ts_results[sensor] = {\n", "                'rolling_anomalies': len(ts_anomalies),\n", "                'rate_change_anomalies': len(roc_anomalies)\n", "            }\n", "\n", "        total_rolling = sum(result['rolling_anomalies'] for result in ts_results.values())\n", "        total_roc = sum(result['rate_change_anomalies'] for result in ts_results.values())\n", "\n", "        print(f\"Time Series Anomalies:\")\n", "        print(f\"  Rolling window anomalies: {total_rolling}\")\n", "        print(f\"  Rate of change anomalies: {total_roc}\")\n", "        return ts_results\n", "\n", "    def predict_future_anomalies(self, new_data):\n", "        \"\"\"\n", "        Predict anomalies in new incoming data\n", "        \"\"\"\n", "        print(\"\\n=== FUTURE ANOMALY PREDICTION ===\")\n", "        if not self.models:\n", "            print(\"Error: Models not trained. Run detection first.\")\n", "            return None\n", "\n", "        # Preprocess new data similarly\n", "        new_data['sensor_id'] = new_data['IP'] + '_' + new_data['Name']\n", "\n", "        # Create pivot table for new data\n", "        new_pivot = new_data.pivot_table(\n", "            index='DateTime',\n", "            columns='sensor_id',\n", "            values='Value',\n", "            aggfunc='mean'\n", "        ).fillna(method='ffill').fillna(method='bfill')\n", "\n", "        # Scale new data using existing scaler\n", "        scaled_new_data = self.models['scaler'].transform(new_pivot.fillna(0).values)\n", "\n", "        # Predict using trained models\n", "        iso_predictions = self.models['isolation_forest'].predict(scaled_new_data)\n", "        svm_predictions = self.models['one_class_svm'].predict(scaled_new_data)\n", "\n", "        # Combine predictions\n", "        predictions = {\n", "            'isolation_forest': iso_predictions,\n", "            'one_class_svm': svm_predictions,\n", "            'combined_anomaly_score': (iso_predictions + svm_predictions) / 2\n", "        }\n", "\n", "        anomaly_count = np.sum(predictions['combined_anomaly_score'] < 0)\n", "        print(f\"Future anomalies predicted: {anomaly_count} out of {len(scaled_new_data)} data points\")\n", "        return predictions\n", "\n", "    def generate_comprehensive_report(self, df, statistical_results, ml_results, ts_results):\n", "        \"\"\"\n", "        Generate a comprehensive anomaly detection report\n", "        \"\"\"\n", "        print(\"\\n\" + \"=\"*50)\n", "        print(\"COMPREHENSIVE ANOMALY DETECTION REPORT\")\n", "        print(\"=\"*50)\n", "\n", "        # Summary statistics\n", "        total_records = len(df)\n", "        total_sensors = df['sensor_id'].nunique()\n", "        total_ips = df['IP'].nunique()\n", "\n", "        print(f\"\\nDATA SUMMARY:\")\n", "        print(f\"Total records: {total_records:,}\")\n", "        print(f\"Total sensors: {total_sensors}\")\n", "        print(f\"Total IP addresses: {total_ips}\")\n", "        print(f\"Time span: {df['DateTime'].min()} to {df['DateTime'].max()}\")\n", "\n", "        # Error analysis\n", "        error_count = df['is_error_value'].sum()\n", "        print(f\"\\nERROR ANALYSIS:\")\n", "        print(f\"Error values detected: {error_count:,} ({error_count/total_records*100:.2f}%)\")\n", "\n", "        # Sensors with high error rates\n", "        high_error_sensors = []\n", "        for sensor, stats in self.sensor_stats.items():\n", "            if stats.get('error_rate', 0) > 10:  # More than 10% error rate\n", "                high_error_sensors.append((sensor, stats['error_rate']))\n", "        if high_error_sensors:\n", "            print(f\"Sensors with high error rates (>10%):\")\n", "            for sensor, rate in sorted(high_error_sensors, key=lambda x: x[1], reverse=True)[:5]:\n", "                print(f\"  {sensor}: {rate:.1f}%\")\n", "\n", "        # Statistical anomalies summary\n", "        print(f\"\\nSTATISTICAL ANOMALIES:\")\n", "        for method, results in statistical_results.items():\n", "            total = sum(results.values())\n", "            print(f\"  {method}: {total:,} anomalies\")\n", "\n", "        # ML anomalies summary\n", "        print(f\"\\nMACHINE LEARNING ANOMALIES:\")\n", "        for method, results in ml_results.items():\n", "            print(f\"  {method}: {results['anomalies']:,} anomalies\")\n", "\n", "        # Time series anomalies summary\n", "        print(f\"\\nTIME SERIES ANOMALIES:\")\n", "        total_rolling = sum(result['rolling_anomalies'] for result in ts_results.values())\n", "        total_roc = sum(result['rate_change_anomalies'] for result in ts_results.values())\n", "        print(f\"  Rolling window: {total_rolling:,} anomalies\")\n", "        print(f\"  Rate of change: {total_roc:,} anomalies\")\n", "\n", "        # Recommendations\n", "        print(f\"\\nRECOMMENDATIONS:\")\n", "        if error_count > 0:\n", "            print(\"• Investigate sensors with consistent -3276.8 values (likely sensor faults)\")\n", "        if high_error_sensors:\n", "            print(\"• Priority maintenance needed for sensors with >10% error rates\")\n", "        print(\"• Set up real-time monitoring using the trained ML models\")\n", "        print(\"• Implement automated alerts for anomaly scores above threshold\")\n", "\n", "        return {\n", "            'total_records': total_records,\n", "            'error_rate': error_count/total_records*100,\n", "            'high_error_sensors': high_error_sensors,\n", "            'statistical_anomalies': statistical_results,\n", "            'ml_anomalies': ml_results,\n", "            'ts_anomalies': ts_results\n", "        }"]}, {"cell_type": "code", "execution_count": 4, "id": "c884a781", "metadata": {}, "outputs": [], "source": ["# Example usage function\n", "def analyze_sensor_data(df):\n", "    \"\"\"\n", "    Main function to run complete anomaly detection analysis\n", "    \"\"\"\n", "    # Initialize detector\n", "    detector = SensorAnomalyDetector()\n", "\n", "    # Step 1: Preprocess data\n", "    processed_df, pivot_df, preprocessing_results = detector.preprocess_data(df)\n", "\n", "    # Step 2: Statistical anomaly detection\n", "    statistical_anomalies, statistical_results = detector.statistical_anomaly_detection(processed_df)\n", "\n", "    # Step 3: ML-based anomaly detection\n", "    ml_results = detector.machine_learning_anomaly_detection(pivot_df)\n", "\n", "    # Step 4: Time series anomaly detection\n", "    ts_results = detector.time_series_anomaly_detection(processed_df)\n", "\n", "    # Step 5: Generate comprehensive report\n", "    report = detector.generate_comprehensive_report(\n", "        processed_df, statistical_results, ml_results, ts_results\n", "    )\n", "\n", "    return detector, report"]}, {"cell_type": "code", "execution_count": 3, "id": "cd8e771c", "metadata": {}, "outputs": [], "source": ["# Load your data\n", "df = pd.read_csv('sensor_pump_data.csv')"]}, {"cell_type": "code", "execution_count": 5, "id": "9e182fe7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DATA PREPROCESSING ===\n", "Original data shape: (341668, 8)\n", "Number of unique sensors: 56\n", "Number of IP addresses: 4\n", "Time range: 2025-05-16 18:28:00 to 2025-05-26 15:20:00\n", "Error values found: 56950\n", "\n", "=== STATISTICAL ANOMALY DETECTION ===\n", "Statistical anomalies detected across all sensors:\n", "  z_score: 1570 anomalies\n", "  iqr: 12596 anomalies\n", "  three_sigma: 1570 anomalies\n", "\n", "=== MACHINE LEARNING ANOMALY DETECTION ===\n", "ML Anomaly Detection Results:\n", "  isolation_forest: 1044 anomalies detected\n", "  one_class_svm: 1042 anomalies detected\n", "  dbscan: 10438 anomalies detected\n", "\n", "=== TIME SERIES ANOMALY DETECTION ===\n", "Time Series Anomalies:\n", "  Rolling window anomalies: 13497\n", "  Rate of change anomalies: 5371\n", "\n", "==================================================\n", "COMPREHENSIVE ANOMALY DETECTION REPORT\n", "==================================================\n", "\n", "DATA SUMMARY:\n", "Total records: 341,668\n", "Total sensors: 56\n", "Total IP addresses: 4\n", "Time span: 2025-05-16 18:28:00 to 2025-05-26 15:20:00\n", "\n", "ERROR ANALYSIS:\n", "Error values detected: 56,950 (16.67%)\n", "\n", "STATISTICAL ANOMALIES:\n", "  z_score: 1,570 anomalies\n", "  iqr: 12,596 anomalies\n", "  three_sigma: 1,570 anomalies\n", "\n", "MACHINE LEARNING ANOMALIES:\n", "  isolation_forest: 1,044 anomalies\n", "  one_class_svm: 1,042 anomalies\n", "  dbscan: 10,438 anomalies\n", "\n", "TIME SERIES ANOMALIES:\n", "  Rolling window: 13,497 anomalies\n", "  Rate of change: 5,371 anomalies\n", "\n", "RECOMMENDATIONS:\n", "• Investigate sensors with consistent -3276.8 values (likely sensor faults)\n", "• Set up real-time monitoring using the trained ML models\n", "• Implement automated alerts for anomaly scores above threshold\n"]}], "source": ["# Run complete analysis\n", "detector, report = analyze_sensor_data(df)"]}, {"cell_type": "code", "execution_count": null, "id": "40c02007", "metadata": {}, "outputs": [], "source": ["# For new data prediction:\n", "new_data = pd.read_csv('new_sensor_data.csv')\n", "predictions = detector.predict_future_anomalies(new_data)"]}, {"cell_type": "code", "execution_count": null, "id": "21214fda", "metadata": {}, "outputs": [], "source": ["# Save the trained detector object for future use\n", "import pickle\n", "with open('anomaly_detector.pkl', 'wb') as f:\n", "    pickle.dump(detector, f)"]}, {"cell_type": "code", "execution_count": null, "id": "cdaea507", "metadata": {}, "outputs": [], "source": ["# How to use with your data:\n", "\"\"\"\n", "# Load your data\n", "df = pd.read_csv('your_sensor_data.csv')\n", "\n", "# Run complete analysis\n", "detector, report = analyze_sensor_data(df)\n", "\n", "# For new data prediction:\n", "new_data = pd.read_csv('new_sensor_data.csv')\n", "predictions = detector.predict_future_anomalies(new_data)\n", "\n", "# Save the trained detector object for future use\n", "import pickle\n", "with open('anomaly_detector.pkl', 'wb') as f:\n", "    pickle.dump(detector, f)\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "7567b0e6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "63f5e77f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ce004f64", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9a181636", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4eb2332e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "87a15b53", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b522111f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "sensor_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}
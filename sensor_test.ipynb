import pandas as pd
import numpy as np

df = pd.read_csv(r'C:\Python_Projects\sensor_pump\sensor_data\Pump *************.csv')

df

df['DateTime'] = pd.to_datetime(df['DateTime'])

processed_df = df.copy()
# processed_df['sensor_id'] = processed_df['IP'] + '_' + processed_df['Name']
# processed_df = processed_df.sort_values(['sensor_id', 'DateTime'])


# Initialize results dictionary
preprocessing_results = {
    'original_shape': df.shape,
    # 'sensors_found': processed_df['sensor_id'].nunique(),
    'ip_addresses': processed_df['IP'].nunique(),
    'sensor_types': processed_df['Name'].nunique(),
    'time_range': (processed_df['DateTime'].min(), processed_df['DateTime'].max())
}

# Identify obvious error values (like -3276.8)
error_values = [-3276.8, -32768, 32767, np.inf, -np.inf]
error_mask = processed_df['Value'].isin(error_values)
preprocessing_results['error_values_found'] = error_mask.sum()


processed_df.columns


# Mark error values
processed_df['is_error_value'] = error_mask

# Create pivot table for time series analysis (one column per sensor)
pivot_df = processed_df.pivot_table(    
    index=['_id', 'DateTime', 'IP'],
    columns='Name',
    values='Value',
    aggfunc='mean'
).fillna(method='ffill').fillna(method='bfill')


pivot_df


pivot_df = pivot_df.reset_index()


pivot_df

pivot_df = pivot_df.sort_values(by=['IP','DateTime'])


pivot_df

pivot_df.shape

# Make a copy to avoid SettingWithCopyWarning
pivot_df = pivot_df.copy()

# Define control limits
control_limits = {
    "Dry Pump speed": (20, 120),
    "Booster speed": (10, 120),
    "DP Temperature": (60, 115),
    "Booster body temperature": (30, 100),
    "Dry Pump Motor Current": (1, 10),
    "Booster Motor Current": (1, 8),
    "Pump Power": (0, 5),
    "Booster Power": (0, 5),
    "Purge Gas Flow Dry Pump": (35, 50),
    "Exhaust Pressure Dry Pump": (-5, 3)
}

# Add status and deviation columns inline
for param, (lcl, ucl) in control_limits.items():
    status_col = f"{param} Status"
    deviation_col = f"{param} Deviation"

    pivot_df.loc[:, status_col] = pivot_df[param].apply(
        lambda x: "Below LCL" if x < lcl else ("Above UCL" if x > ucl else "In Range")
    )
    pivot_df.loc[:, deviation_col] = pivot_df[param].apply(
        lambda x: x - lcl if x < lcl else (x - ucl if x > ucl else 0)
    )

    # Reorder columns to insert status and deviation right after the parameter
    cols = list(pivot_df.columns)
    idx = cols.index(param)
    for col in [status_col, deviation_col]:
        cols.remove(col)
        cols.insert(idx + 1, col)
        idx += 1
    pivot_df = pivot_df[cols]


pivot_df

pivot_df.shape

# Get all columns that end with 'Status'
status_columns = [col for col in pivot_df.columns if col.endswith('Status')]

# Print unique values for each status column
for col in status_columns:
    print(f"Unique values in '{col}': {pivot_df[col].unique()}")


pivot_df['Dry Pump Motor Current Deviation'].unique()

pivot_df[pivot_df['Dry Pump Motor Current Status'] == "Below LCL"]

cols

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

import warnings

# Suppress common pandas warnings
warnings.simplefilter(action='ignore', category=pd.errors.SettingWithCopyWarning)
warnings.filterwarnings('ignore')



# ✅ Replace these with actual file paths on your system
file_paths = [
    r'C:\Python_Projects\sensor_pump\sensor_data\Pump *************.csv',
    r'C:\Python_Projects\sensor_pump\sensor_data\Pump *************.csv',
    r'C:\Python_Projects\sensor_pump\sensor_data\Pump *************.csv',
    r'C:\Python_Projects\sensor_pump\sensor_data\Pump *************.csv'
]




# Extract IPs from file names
ip_addresses = [os.path.basename(path).split()[1].replace('.csv', '') for path in file_paths]

ip_addresses

# Features to plot
features = [
    'Booster Motor Current', 'Booster Power', 'Booster body temperature', 'Booster speed',
    'DP Temperature', 'Dry Pump Motor Current', 'Dry Pump speed',
    'Exhaust Pressure Dry Pump', 'Pump Power', 'Purge Gas Flow Dry Pump'
]

# Date ranges
end_date = pd.Timestamp('2025-06-02 07:20:00')
start_date_10_days = end_date - pd.Timedelta(days=10)
start_date_1_month = pd.Timestamp('2025-05-20 07:20:00')
end_date_1_month = pd.Timestamp('2025-05-22 07:20:00')

# Control limits
control_limits = {
    "Dry Pump speed": (20, 120), "Booster speed": (10, 120), "DP Temperature": (60, 115),
    "Booster body temperature": (30, 100), "Dry Pump Motor Current": (1, 10),
    "Booster Motor Current": (1, 8), "Pump Power": (0, 5), "Booster Power": (0, 5),
    "Purge Gas Flow Dry Pump": (35, 50), "Exhaust Pressure Dry Pump": (-5, 3)
}


# Read and process all CSVs
all_data = []
for file_path in file_paths:
    df = pd.read_csv(file_path)
    df['DateTime'] = pd.to_datetime(df['DateTime'])
    df = df[~df['Value'].isin([-3276.8, -32768, 32767, np.inf, -np.inf])]
    df['is_error_value'] = False

    pivot_df = df.pivot_table(index=['_id', 'DateTime', 'IP'], columns='Name', values='Value', aggfunc='mean')
    # pivot_df = pivot_df.fillna(method='ffill').fillna(method='bfill').reset_index()
    pivot_df = pivot_df.ffill().bfill().reset_index()  
    pivot_df = pivot_df.sort_values(by=['IP', 'DateTime'])

    for param, (lcl, ucl) in control_limits.items():
        pivot_df[f"{param} Status"] = pivot_df[param].apply(
            lambda x: "Below LCL" if x < lcl else ("Above UCL" if x > ucl else "In Range"))
        pivot_df[f"{param} Deviation"] = pivot_df[param].apply(
            lambda x: x - lcl if x < lcl else (x - ucl if x > ucl else 0))

    all_data.append(pivot_df)

# Combine all IP data
combined_df = pd.concat(all_data, ignore_index=True)


combined_df

combined_df['IP'].unique()

# Filter data for plotting
plot_data = {}
for ip in ip_addresses:
    ip_data = combined_df[combined_df['IP'] == ip]
    if ip.endswith('153'):
        plot_data[ip] = {
            '10_days': ip_data[(ip_data['DateTime'] >= start_date_10_days) & (ip_data['DateTime'] <= end_date)],
            '1_month': ip_data[(ip_data['DateTime'] >= start_date_1_month) & (ip_data['DateTime'] <= end_date_1_month)]
        }
    else:
        plot_data[ip] = {
            '10_days': ip_data[(ip_data['DateTime'] >= start_date_10_days) & (ip_data['DateTime'] <= end_date)]
        }

# Plotting
for feature in features:
    plt.figure(figsize=(12, 6))
    for ip in ip_addresses:
        if ip.endswith('153'):
            plt.plot(plot_data[ip]['10_days']['DateTime'], plot_data[ip]['10_days'][feature], label=f'{ip} (10 days)')
            plt.plot(plot_data[ip]['1_month']['DateTime'], plot_data[ip]['1_month'][feature], label=f'{ip} (1 month)')
        else:
            plt.plot(plot_data[ip]['10_days']['DateTime'], plot_data[ip]['10_days'][feature], label=f'{ip} (10 days)')
    plt.title(f'{feature} over Time')
    plt.xlabel('DateTime')
    plt.ylabel(feature)
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.show()


import plotly.graph_objects as go

# Filter data for plotting
plot_data = {}
for ip in ip_addresses:
    ip_data = combined_df[combined_df['IP'] == ip]
    if ip.endswith('153'):
        plot_data[ip] = {
            '10_days': ip_data[(ip_data['DateTime'] >= start_date_10_days) & (ip_data['DateTime'] <= end_date)],
            '1_month': ip_data[(ip_data['DateTime'] >= start_date_1_month) & (ip_data['DateTime'] <= end_date_1_month)]
        }
    else:
        plot_data[ip] = {
            '10_days': ip_data[(ip_data['DateTime'] >= start_date_10_days) & (ip_data['DateTime'] <= end_date)]
        }

# Plotting
for feature in features:
    fig = go.Figure()
    for ip in ip_addresses:
        if ip.endswith('153'):
            fig.add_trace(go.Scatter(
                x=plot_data[ip]['10_days']['DateTime'],
                y=plot_data[ip]['10_days'][feature],
                name=f'{ip} (10 days)',
                mode='lines'
            ))
            fig.add_trace(go.Scatter(
                x=plot_data[ip]['1_month']['DateTime'],
                y=plot_data[ip]['1_month'][feature],
                name=f'{ip} (1 month)',
                mode='lines'
            ))
        else:
            fig.add_trace(go.Scatter(
                x=plot_data[ip]['10_days']['DateTime'],
                y=plot_data[ip]['10_days'][feature],
                name=f'{ip} (10 days)',
                mode='lines'
            ))
    fig.update_layout(
        title=f'{feature} over Time',
        xaxis_title='DateTime',
        yaxis_title=feature,
        legend=dict(orientation='h')
    )
    fig.update_xaxes(
        range=[min(start_date_1_month, start_date_10_days), end_date]
    )
    fig.show()

end_date = pd.Timestamp('2025-06-02 07:20:00')
start_date_3_hours = end_date - pd.Timedelta(hours=3)
start_date_5_hours = end_date - pd.Timedelta(hours=5)
start_date_2_hours = end_date - pd.Timedelta(hours=2)

import plotly.graph_objects as go

# Filter data for plotting
plot_data = {}
for ip in ip_addresses:
    ip_data = combined_df[combined_df['IP'] == ip]
    if ip.endswith('153'):
        plot_data[ip] = {
            '3_hours': ip_data[(ip_data['DateTime'] >= start_date_3_hours) & (ip_data['DateTime'] <= end_date)]
        }
    else:
        plot_data[ip] = {
            '3_hours': ip_data[(ip_data['DateTime'] >= start_date_3_hours) & (ip_data['DateTime'] <= end_date)]
        }

# Plotting
for feature in features:
    fig = go.Figure()
    for ip in ip_addresses:
        if ip.endswith('153'):
            fig.add_trace(go.Scatter(
                x=plot_data[ip]['3_hours']['DateTime'],
                y=plot_data[ip]['3_hours'][feature],
                name=f'{ip} (3 hours)',
                mode='lines'
            ))
        else:
            fig.add_trace(go.Scatter(
                x=plot_data[ip]['3_hours']['DateTime'],
                y=plot_data[ip]['3_hours'][feature],
                name=f'{ip} (3 hours)',
                mode='lines'
            ))
    fig.update_layout(
        title=f'{feature} over Time',
        xaxis_title='DateTime',
        yaxis_title=feature,
        legend=dict(orientation='h')
    )
    fig.update_xaxes(
        range=[start_date_3_hours, end_date]  # Set x-axis limits to 3 hours range
    )
    fig.show()

import plotly.graph_objects as go

# Filter data for plotting
plot_data = {}
for ip in ip_addresses:
    ip_data = combined_df[combined_df['IP'] == ip]
    if ip.endswith('153'):
        plot_data[ip] = {
            '2_hours': ip_data[(ip_data['DateTime'] >= start_date_5_hours) & (ip_data['DateTime'] <= end_date)]
        }
    else:
        plot_data[ip] = {
            '2_hours': ip_data[(ip_data['DateTime'] >= start_date_5_hours) & (ip_data['DateTime'] <= end_date)]
        }

# Plotting
for feature in features:
    fig = go.Figure()
    for ip in ip_addresses:
        if ip.endswith('153'):
            fig.add_trace(go.Scatter(
                x=plot_data[ip]['2_hours']['DateTime'],
                y=plot_data[ip]['2_hours'][feature],
                name=f'{ip} (2 hours)',
                mode='lines'
            ))
        else:
            fig.add_trace(go.Scatter(
                x=plot_data[ip]['2_hours']['DateTime'],
                y=plot_data[ip]['2_hours'][feature],
                name=f'{ip} (2 hours)',
                mode='lines'
            ))
    fig.update_layout(
        title=f'{feature} over Time',
        xaxis_title='DateTime',
        yaxis_title=feature,
        legend=dict(orientation='h')
    )
    fig.update_xaxes(
        range=[start_date_2_hours, end_date]  
    )
    fig.show()

import plotly.graph_objects as go

end_date = pd.Timestamp('2025-05-26 07:20:00')
start_date_3_hours = end_date - pd.Timedelta(hours=3)

# Filter data for plotting
plot_data = {}
for ip in ip_addresses:
    ip_data = combined_df[combined_df['IP'] == ip]
    if ip.endswith('153'):
        plot_data[ip] = {
            '3_hours': ip_data[(ip_data['DateTime'] >= start_date_3_hours) & (ip_data['DateTime'] <= end_date)]
        }
    else:
        plot_data[ip] = {
            '3_hours': ip_data[(ip_data['DateTime'] >= start_date_3_hours) & (ip_data['DateTime'] <= end_date)]
        }

# Plotting
for feature in features:
    fig = go.Figure()
    for ip in ip_addresses:
        if ip.endswith('153'):
            fig.add_trace(go.Scatter(
                x=plot_data[ip]['3_hours']['DateTime'],
                y=plot_data[ip]['3_hours'][feature],
                name=f'{ip} (3 hours)',
                mode='lines'
            ))
        else:
            fig.add_trace(go.Scatter(
                x=plot_data[ip]['3_hours']['DateTime'],
                y=plot_data[ip]['3_hours'][feature],
                name=f'{ip} (3 hours)',
                mode='lines'
            ))
    fig.update_layout(
        title=f'{feature} over Time',
        xaxis_title='DateTime',
        yaxis_title=feature,
        legend=dict(orientation='h')
    )
    fig.update_xaxes(
        range=[start_date_3_hours, end_date]  # Set x-axis limits to 3 hours range
    )
    fig.show()

import plotly.graph_objects as go

end_date = pd.Timestamp('2025-05-27 07:20:00')
start_date_3_hours = end_date - pd.Timedelta(hours=3)

# Filter data for plotting
plot_data = {}
for ip in ip_addresses:
    ip_data = combined_df[combined_df['IP'] == ip]
    if ip.endswith('153'):
        plot_data[ip] = {
            '3_hours': ip_data[(ip_data['DateTime'] >= start_date_3_hours) & (ip_data['DateTime'] <= end_date)]
        }
    else:
        plot_data[ip] = {
            '3_hours': ip_data[(ip_data['DateTime'] >= start_date_3_hours) & (ip_data['DateTime'] <= end_date)]
        }

# Plotting
for feature in features:
    fig = go.Figure()
    for ip in ip_addresses:
        if ip.endswith('153'):
            fig.add_trace(go.Scatter(
                x=plot_data[ip]['3_hours']['DateTime'],
                y=plot_data[ip]['3_hours'][feature],
                name=f'{ip} (3 hours)',
                mode='lines'
            ))
        else:
            fig.add_trace(go.Scatter(
                x=plot_data[ip]['3_hours']['DateTime'],
                y=plot_data[ip]['3_hours'][feature],
                name=f'{ip} (3 hours)',
                mode='lines'
            ))
    fig.update_layout(
        title=f'{feature} over Time',
        xaxis_title='DateTime',
        yaxis_title=feature,
        legend=dict(orientation='h')
    )
    fig.update_xaxes(
        range=[start_date_3_hours, end_date]  # Set x-axis limits to 3 hours range
    )
    fig.show()

import plotly.graph_objects as go

end_date = pd.Timestamp('2025-05-26 07:20:00')
start_date_3_hours = end_date - pd.Timedelta(hours=3)

# Filter data for plotting
plot_data = {}
for ip in ip_addresses:
    ip_data = combined_df[combined_df['IP'] == ip]
    if ip.endswith('153'):
        plot_data[ip] = {
            '3_hours': ip_data[(ip_data['DateTime'] >= start_date_3_hours) & (ip_data['DateTime'] <= end_date)]
        }
    else:
        plot_data[ip] = {
            '3_hours': ip_data[(ip_data['DateTime'] >= start_date_3_hours) & (ip_data['DateTime'] <= end_date)]
        }

# Plotting
for feature in features:
    fig = go.Figure()
    for ip in ip_addresses:
        if ip.endswith('153'):
            fig.add_trace(go.Scatter(
                x=plot_data[ip]['3_hours']['DateTime'],
                y=plot_data[ip]['3_hours'][feature],
                name=f'{ip} (3 hours)',
                mode='lines'
            ))
        else:
            fig.add_trace(go.Scatter(
                x=plot_data[ip]['3_hours']['DateTime'],
                y=plot_data[ip]['3_hours'][feature],
                name=f'{ip} (3 hours)',
                mode='lines'
            ))
    fig.update_layout(
        title=f'{feature} over Time',
        xaxis_title='DateTime',
        yaxis_title=feature,
        legend=dict(orientation='h')
    )
    fig.update_xaxes(
        range=[start_date_3_hours, end_date]  # Set x-axis limits to 3 hours range
    )
    fig.show()